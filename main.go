package main

import (
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/charmbracelet/bubbles/list"
	"github.com/charmbracelet/bubbles/progress"
	"github.com/charmbracelet/bubbles/spinner"
	"github.com/charmbracelet/bubbles/table"
	"github.com/charmbracelet/bubbles/textinput"
	"github.com/charmbracelet/bubbles/viewport"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
)

// Tab constants
const (
	MainTab = iota
	ICITab
	DiagTab
)

// Tab names and icons - using standard characters
var (
	tabNames = []string{"Home", "Links", "Diagnostics"}
	tabIcons = []string{"*", "#", "@"}
)

// Model represents the main application state
type model struct {
	// Global state
	width      int
	height     int
	loading    bool
	currentTab int

	// Loading screen
	prog progress.Model

	// Modal system
	modalActive  bool
	modalContent string
	showingHelp  bool

	// Status
	status string

	// ICI Tab
	iciList        list.Model
	iciTesting     bool
	iciSpinner     spinner.Model
	iciTestResult  string
	iciTestHistory []string

	// Diag Tab
	diagMachineInput textinput.Model
	diagRunning      bool
	diagSpinner      spinner.Model
	diagOutput       viewport.Model
	diagTable        table.Model
	diagHistoryList  list.Model
}

// Initialize the application
func (m model) Init() tea.Cmd {
	return tea.Batch(
		m.prog.Init(),
		m.iciSpinner.Tick,
		m.diagSpinner.Tick,
		tea.Tick(time.Millisecond*50, func(t time.Time) tea.Msg {
			return tickMsg(t)
		}),
	)
}

// Custom message types
type tickMsg time.Time

// List item implementation for bubbles/list
type listItem struct {
	title, desc string
}

func (i listItem) Title() string       { return i.title }
func (i listItem) Description() string { return i.desc }
func (i listItem) FilterValue() string { return i.title }

// Update handles all events and state changes
func (m model) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	var cmd tea.Cmd
	var cmds []tea.Cmd

	switch msg := msg.(type) {
	case tea.WindowSizeMsg:
		m.width = msg.Width
		m.height = msg.Height

		// Update component sizes with minimum constraints
		minWidth := max(20, msg.Width-4)
		minHeight := max(5, msg.Height-15)

		m.prog.Width = minWidth

		// ICI list sizing
		if minWidth > 0 && minHeight > 0 {
			m.iciList.SetSize(minWidth, minHeight)
		}

		// Diag components sizing
		m.diagOutput.Width = minWidth
		m.diagOutput.Height = max(5, min(15, msg.Height/3))

		// Diag history list sizing
		historyHeight := max(3, min(10, msg.Height/4))
		if minWidth > 0 && historyHeight > 0 {
			m.diagHistoryList.SetSize(minWidth, historyHeight)
		}

		// Update table width
		if minWidth > 60 {
			// Adjust table column widths for larger screens
			m.diagTable = table.New(
				table.WithColumns([]table.Column{
					{Title: "Host", Width: minWidth / 3},
					{Title: "Status", Width: 15},
					{Title: "Details", Width: minWidth - (minWidth / 3) - 15 - 10},
				}),
				table.WithRows(m.diagTable.Rows()),
				table.WithFocused(true),
				table.WithHeight(max(3, min(8, msg.Height/4))),
			)
		}

		return m, nil

	case tea.KeyMsg:
		// Global key handling
		switch msg.String() {
		case "ctrl+c", "q", "esc":
			if m.showingHelp {
				m.showingHelp = false
				m.modalActive = false
				return m, nil
			}
			return m, tea.Quit
		case "?", "h":
			if !m.modalActive && !m.loading {
				m.showingHelp = true
				m.modalActive = true
				m.modalContent = getHelpText()
				return m, nil
			}
		}

		// Modal handling
		if m.modalActive {
			if m.showingHelp {
				// Help modal - any key closes it
				m.showingHelp = false
				m.modalActive = false
				m.modalContent = ""
				return m, nil
			} else {
				// Confirmation modal
				switch msg.String() {
				case "y", "Y":
					m.modalActive = false
					return m.handleModalConfirm()
				case "n", "N":
					m.modalActive = false
					m.modalContent = ""
					return m, nil
				}
				return m, nil
			}
		}

		// Loading screen handling
		if m.loading {
			if msg.String() == "q" {
				m.loading = false
				m.status = "Ready"
			}
			return m, nil
		}

		// Tab navigation
		switch msg.String() {
		case "left", "h":
			m.currentTab = (m.currentTab - 1 + len(tabNames)) % len(tabNames)
		case "right", "l":
			m.currentTab = (m.currentTab + 1) % len(tabNames)
		case "1":
			m.currentTab = MainTab
		case "2":
			m.currentTab = ICITab
		case "3":
			m.currentTab = DiagTab
		}

		// Tab-specific key handling
		switch m.currentTab {
		case ICITab:
			return m.updateICITab(msg)
		case DiagTab:
			return m.updateDiagTab(msg)
		}

	case tea.MouseMsg:
		// Handle mouse clicks on tabs
		if msg.Action == tea.MouseActionPress && msg.Button == tea.MouseButtonLeft && msg.Y == 0 {
			// Calculate which tab was clicked based on X position
			tabWidth := 8 // Approximate width of each tab
			clickedTab := msg.X / tabWidth
			if clickedTab >= 0 && clickedTab < len(tabNames) {
				m.currentTab = clickedTab
			}
		}

	case tickMsg:
		// Handle loading progress
		if m.loading {
			if m.prog.Percent() >= 1.0 {
				m.loading = false
				m.status = "Ready"
			} else {
				cmd = m.prog.IncrPercent(0.02) // Increment by 2% each tick
				cmds = append(cmds, cmd)
			}
		}

		// Continue ticking
		cmds = append(cmds, tea.Tick(time.Millisecond*50, func(t time.Time) tea.Msg {
			return tickMsg(t)
		}))

	case iciTestCompleteMsg:
		m.iciTesting = false
		m.iciTestResult = msg.result
		m.status = "ICI test completed"

		// Add to history
		timestamp := time.Now().Format("2006-01-02 15:04:05")
		historyEntry := fmt.Sprintf("%s | %s | %s", msg.link, msg.status, timestamp)
		m.iciTestHistory = append(m.iciTestHistory, historyEntry)

	case diagTestCompleteMsg:
		m.diagRunning = false
		m.status = "Diagnostics completed"

		// Update output
		m.diagOutput.SetContent(msg.output)

		// Add to table with appropriate details
		var details string
		if msg.status == "Online" {
			details = "Ping successful"
		} else {
			details = "Connection timeout"
		}
		newRow := table.Row{msg.machine, msg.status, details}
		m.diagTable.SetRows(append(m.diagTable.Rows(), newRow))

		// Add to history
		timestamp := time.Now().Format("2006-01-02 15:04:05")
		historyItem := listItem{
			title: fmt.Sprintf("%s - %s", msg.machine, timestamp),
			desc:  fmt.Sprintf("Status: %s", msg.status),
		}

		// Insert at beginning of history
		items := m.diagHistoryList.Items()
		newItems := append([]list.Item{historyItem}, items...)
		m.diagHistoryList.SetItems(newItems)

		// Clear input
		m.diagMachineInput.SetValue("")
	}

	// Update spinners
	m.iciSpinner, cmd = m.iciSpinner.Update(msg)
	cmds = append(cmds, cmd)

	m.diagSpinner, cmd = m.diagSpinner.Update(msg)
	cmds = append(cmds, cmd)

	return m, tea.Batch(cmds...)
}

// View renders the application with proper bounds checking
func (m model) View() string {
	if m.loading {
		return m.loadingView()
	}

	// Render tabs
	tabs := m.renderTabs()

	// Render current tab content
	var content string
	switch m.currentTab {
	case MainTab:
		content = m.mainTabView()
	case ICITab:
		content = m.iciTabView()
	case DiagTab:
		content = m.diagTabView()
	}

	// Render status bar
	statusBar := m.renderStatusBar()

	// Combine all elements
	full := lipgloss.JoinVertical(lipgloss.Left, tabs, content, statusBar)

	// Ensure we don't exceed screen bounds
	lines := strings.Split(full, "\n")
	if len(lines) > m.height {
		// Truncate to fit height
		lines = lines[:m.height]
		full = strings.Join(lines, "\n")
	}

	// Ensure each line doesn't exceed width
	var boundedLines []string
	for _, line := range lines {
		cleanLine := stripAnsiCodes(line)
		if lipgloss.Width(cleanLine) > m.width {
			// Truncate line to fit width
			truncated := truncateToWidth(line, m.width)
			boundedLines = append(boundedLines, truncated)
		} else {
			boundedLines = append(boundedLines, line)
		}
	}
	full = strings.Join(boundedLines, "\n")

	// Add modal overlay if active
	if m.modalActive {
		full = m.overlayModal(full)
	}

	return full
}

// Flashy and colorful styles inspired by Lipgloss examples
var (
	// Color palette - vibrant and modern
	primaryColor   = lipgloss.AdaptiveColor{Light: "#FF6B9D", Dark: "#FF6B9D"} // Hot pink
	secondaryColor = lipgloss.AdaptiveColor{Light: "#4ECDC4", Dark: "#4ECDC4"} // Teal
	accentColor    = lipgloss.AdaptiveColor{Light: "#FFE66D", Dark: "#FFE66D"} // Yellow
	successColor   = lipgloss.AdaptiveColor{Light: "#95E1D3", Dark: "#95E1D3"} // Mint green
	errorColor     = lipgloss.AdaptiveColor{Light: "#F38BA8", Dark: "#F38BA8"} // Coral
	purpleColor    = lipgloss.AdaptiveColor{Light: "#B794F6", Dark: "#B794F6"} // Purple
	gradientStart  = lipgloss.AdaptiveColor{Light: "#667eea", Dark: "#667eea"} // Blue
	gradientEnd    = lipgloss.AdaptiveColor{Light: "#764ba2", Dark: "#764ba2"} // Purple

	// Tab styles following official Lipgloss tabs pattern
	inactiveTabBorder = tabBorderWithBottom("┴", "─", "┴")
	activeTabBorder   = tabBorderWithBottom("┘", " ", "└")

	highlightColor = lipgloss.AdaptiveColor{Light: "#874BFD", Dark: "#7D56F4"}

	inactiveTabStyle = lipgloss.NewStyle().
				Border(inactiveTabBorder, true).
				BorderForeground(highlightColor).
				Padding(0, 1)

	activeTabStyle = inactiveTabStyle.
			Border(activeTabBorder, true)

	// Tab gap style for filling remaining width with bottom border
	tabGapBorder = lipgloss.Border{
		Top:         "─",
		Bottom:      "─",
		Left:        "",
		Right:       "",
		TopLeft:     "",
		TopRight:    "",
		BottomLeft:  "",
		BottomRight: "",
	}

	tabGapStyle = lipgloss.NewStyle().
			Border(tabGapBorder, true, false, true, false).
			BorderForeground(highlightColor)

	// Content styles following official tabs pattern
	windowStyle = lipgloss.NewStyle().
			BorderForeground(highlightColor).
			Padding(2, 0).
			Align(lipgloss.Center).
			Border(lipgloss.NormalBorder()).
			UnsetBorderTop()

	contentStyle = lipgloss.NewStyle().
			Padding(1, 2)

	headerStyle = lipgloss.NewStyle().
			Bold(true).
			Foreground(lipgloss.Color("#FFFFFF")).
			Background(primaryColor).
			Align(lipgloss.Center).
			Border(lipgloss.ThickBorder()).
			BorderForeground(primaryColor).
			Padding(2, 4).
			MarginBottom(1)

	bodyStyle = lipgloss.NewStyle().
			Italic(true).
			Foreground(lipgloss.AdaptiveColor{Light: "#4B5563", Dark: "#D1D5DB"}).
			Align(lipgloss.Center).
			MaxHeight(8).
			Padding(1, 2)

	boxStyle = lipgloss.NewStyle().
			Border(lipgloss.DoubleBorder()).
			BorderForeground(secondaryColor).
			Background(lipgloss.AdaptiveColor{Light: "#F0FDF4", Dark: "#064E3B"}).
			Foreground(lipgloss.AdaptiveColor{Light: "#059669", Dark: "#10B981"}).
			Padding(2, 3).
			Align(lipgloss.Center).
			Bold(true)

	// Button styles - more vibrant
	activeButtonStyle = lipgloss.NewStyle().
				Bold(true).
				Foreground(lipgloss.Color("#FFFFFF")).
				Background(accentColor).
				Padding(1, 3).
				Margin(1, 0).
				Border(lipgloss.RoundedBorder()).
				BorderForeground(accentColor)

	successButtonStyle = lipgloss.NewStyle().
				Bold(true).
				Foreground(lipgloss.Color("#FFFFFF")).
				Background(successColor).
				Padding(1, 3).
				Margin(1, 0).
				Border(lipgloss.RoundedBorder()).
				BorderForeground(successColor)

	// Input styles with better contrast
	inputStyle = lipgloss.NewStyle().
			Border(lipgloss.ThickBorder()).
			BorderForeground(secondaryColor).
			Background(lipgloss.AdaptiveColor{Light: "#FFFFFF", Dark: "#1F2937"}).
			Foreground(lipgloss.AdaptiveColor{Light: "#111827", Dark: "#F9FAFB"}).
			Padding(1, 2).
			Margin(1, 0)

	// Loading styles with gradient effect
	loadingStyle = lipgloss.NewStyle().
			Align(lipgloss.Center).
			Foreground(primaryColor).
			Bold(true).
			Padding(2, 4).
			Border(lipgloss.RoundedBorder()).
			BorderForeground(primaryColor)

	// Modal styles - more dramatic
	modalStyle = lipgloss.NewStyle().
			Border(lipgloss.ThickBorder()).
			BorderForeground(purpleColor).
			Background(lipgloss.AdaptiveColor{Light: "#FEFEFE", Dark: "#1F2937"}).
			Foreground(lipgloss.AdaptiveColor{Light: "#111827", Dark: "#F9FAFB"}).
			Padding(2, 4).
			Align(lipgloss.Center).
			Bold(true)

	// Status bar style - gradient-like
	statusStyle = lipgloss.NewStyle().
			Foreground(lipgloss.Color("#FFFFFF")).
			Background(gradientStart).
			Padding(1, 2).
			Bold(true)

	// Result styles for different states
	successResultStyle = lipgloss.NewStyle().
				Border(lipgloss.RoundedBorder()).
				BorderForeground(successColor).
				Background(lipgloss.AdaptiveColor{Light: "#F0FDF4", Dark: "#064E3B"}).
				Foreground(successColor).
				Padding(1, 2).
				Bold(true)

	errorResultStyle = lipgloss.NewStyle().
				Border(lipgloss.RoundedBorder()).
				BorderForeground(errorColor).
				Background(lipgloss.AdaptiveColor{Light: "#FEF2F2", Dark: "#7F1D1D"}).
				Foreground(errorColor).
				Padding(1, 2).
				Bold(true)
)

// Initialize the model with default values
func initialModel() model {
	// Create progress bar
	prog := progress.New(progress.WithDefaultGradient())
	prog.Width = 40

	// Create colorful spinners
	iciSpinner := spinner.New()
	iciSpinner.Spinner = spinner.Globe
	iciSpinner.Style = lipgloss.NewStyle().Foreground(primaryColor).Bold(true)

	diagSpinner := spinner.New()
	diagSpinner.Spinner = spinner.Points
	diagSpinner.Style = lipgloss.NewStyle().Foreground(accentColor).Bold(true)

	// Create ICI list
	iciItems := []list.Item{
		listItem{title: "Fiber Link 1", desc: "Primary fiber connection"},
		listItem{title: "Fiber Link 2", desc: "Secondary fiber connection"},
		listItem{title: "Copper Link 1", desc: "Primary copper connection"},
		listItem{title: "Copper Link 2", desc: "Secondary copper connection"},
	}
	iciList := list.New(iciItems, list.NewDefaultDelegate(), 0, 0)
	iciList.Title = "Select Link to Test"
	iciList.SetShowStatusBar(false)
	iciList.SetFilteringEnabled(false)

	// Create text input for diag
	diagInput := textinput.New()
	diagInput.Placeholder = "Enter machine name..."
	diagInput.Focus()
	diagInput.CharLimit = 50
	diagInput.Width = 30

	// Create viewport for diag output
	diagOutput := viewport.New(0, 10)
	diagOutput.SetContent("No output yet...")

	// Create table for diag results
	diagTable := table.New(
		table.WithColumns([]table.Column{
			{Title: "Host", Width: 20},
			{Title: "Status", Width: 10},
			{Title: "Details", Width: 30},
		}),
		table.WithRows([]table.Row{}),
		table.WithFocused(true),
		table.WithHeight(5),
	)

	// Create history list for diag
	diagHistoryList := list.New([]list.Item{}, list.NewDefaultDelegate(), 0, 0)
	diagHistoryList.Title = "Command History"
	diagHistoryList.SetShowStatusBar(false)
	diagHistoryList.SetFilteringEnabled(false)

	return model{
		loading:          true,
		currentTab:       MainTab,
		prog:             prog,
		status:           "Loading...",
		iciList:          iciList,
		iciSpinner:       iciSpinner,
		iciTestHistory:   []string{},
		diagMachineInput: diagInput,
		diagSpinner:      diagSpinner,
		diagOutput:       diagOutput,
		diagTable:        diagTable,
		diagHistoryList:  diagHistoryList,
	}
}

// Loading screen view with enhanced colors
func (m model) loadingView() string {
	// Loading title
	title := lipgloss.NewStyle().
		Bold(true).
		Foreground(primaryColor).
		Align(lipgloss.Center).
		Render("ICI Network Diagnostics")

	subtitle := lipgloss.NewStyle().
		Italic(true).
		Foreground(secondaryColor).
		Align(lipgloss.Center).
		Render("Initializing awesome network tools...")

	// Enhanced progress bar with styling
	progressBar := lipgloss.NewStyle().
		Padding(1, 2).
		Align(lipgloss.Center).
		Border(lipgloss.RoundedBorder()).
		BorderForeground(accentColor).
		Render(m.prog.View())

	// Colorful skip instruction
	skipText := lipgloss.NewStyle().
		Foreground(lipgloss.AdaptiveColor{Light: "#6B7280", Dark: "#9CA3AF"}).
		Align(lipgloss.Center).
		Render("Press 'q' to skip loading")

	// Loading messages
	loadingMessages := []string{
		"Setting up interfaces...",
		"Preparing network diagnostics...",
		"Loading CLI tools...",
		"Applying styles...",
		"Almost ready to launch!",
	}

	// Select message based on progress
	messageIndex := int(m.prog.Percent() * float64(len(loadingMessages)))
	if messageIndex >= len(loadingMessages) {
		messageIndex = len(loadingMessages) - 1
	}

	message := lipgloss.NewStyle().
		Foreground(purpleColor).
		Align(lipgloss.Center).
		Italic(true).
		Render(loadingMessages[messageIndex])

	return loadingStyle.Render(
		lipgloss.JoinVertical(lipgloss.Center,
			title,
			subtitle,
			"",
			progressBar,
			"",
			message,
			"",
			skipText))
}

// Render tabs using official Lipgloss tabs pattern
func (m model) renderTabs() string {
	var renderedTabs []string

	for i, t := range tabNames {
		var tabText string
		var style lipgloss.Style
		isFirst, isLast, isActive := i == 0, i == len(tabNames)-1, i == m.currentTab

		// Responsive tab text based on available width (adjusted for new tab styling)
		if m.width >= 130 {
			tabText = tabIcons[i] + " " + t
		} else if m.width >= 125 {
			tabText = tabIcons[i]
		} else if m.width >= 30 {
			tabText = fmt.Sprintf("%d", i+1)
		} else {
			// Very minimal for tiny screens
			tabText = fmt.Sprintf("%d", i+1)
		}

		// Apply official Lipgloss tabs styling
		if m.width >= 30 {
			// Use full border styling for larger screens
			if isActive {
				style = activeTabStyle
			} else {
				style = inactiveTabStyle
			}

			// Adjust borders for first/last tabs (following official pattern)
			border, _, _, _, _ := style.GetBorder()
			if isFirst && isActive {
				border.BottomLeft = "│"
			} else if isFirst && !isActive {
				border.BottomLeft = "├"
			} else if isLast && isActive {
				border.BottomRight = "│"
			} else if isLast && !isActive {
				border.BottomRight = "┤"
			}
			style = style.Border(border)
		} else {
			// Minimal styling for very small screens
			if isActive {
				style = lipgloss.NewStyle().
					Bold(true).
					Foreground(highlightColor)
			} else {
				style = lipgloss.NewStyle().
					Foreground(lipgloss.AdaptiveColor{Light: "#6C7B7F", Dark: "#9CA3AF"})
			}
		}

		renderedTabs = append(renderedTabs, style.Render(tabText))
	}

	// Create tabs row following official Lipgloss pattern
	tabRow := lipgloss.JoinHorizontal(lipgloss.Top, renderedTabs...)

	// Calculate remaining width and create gap with bottom border
	remainingWidth := max(0, m.width-lipgloss.Width(tabRow)-2)
	gap := tabGapStyle.Render(strings.Repeat(" ", remainingWidth))

	// Join tabs and gap together (following official pattern)
	finalRow := lipgloss.JoinHorizontal(lipgloss.Bottom, tabRow, gap)

	// Ensure width constraint
	if lipgloss.Width(finalRow) > m.width {
		return lipgloss.PlaceHorizontal(m.width, lipgloss.Left, finalRow)
	}

	return finalRow
}

// Main tab view with enhanced colors and animations
func (m model) mainTabView() string {
	width := m.width
	if width < 10 {
		width = 10
	}

	// Calculate available height for content (subtract tabs, status bar, padding)
	availableHeight := max(5, m.height-8)

	// Responsive header - shorter for small screens
	var headerText string
	if width >= 60 {
		headerText = "Welcome to ICI Network Diagnostics"
	} else if width >= 40 {
		headerText = "ICI Diagnostics"
	} else {
		headerText = "ICI"
	}
	header := headerStyle.Width(max(10, width-8)).Render(headerText)

	// Responsive body content
	var bodyContent string
	if availableHeight >= 15 && width >= 50 {
		// Full content for larger screens
		// Build content line by line for consistent width
		var contentLines []string

		contentLines = append(contentLines, lipgloss.NewStyle().
			Foreground(secondaryColor).
			Bold(true).
			Render("Navigate between diagnostic tools"))

		contentLines = append(contentLines, "") // Empty line

		contentLines = append(contentLines, lipgloss.NewStyle().
			Foreground(primaryColor).
			Bold(true).
			Render("# ICI")+" - Test links")

		contentLines = append(contentLines, lipgloss.NewStyle().
			Foreground(accentColor).
			Bold(true).
			Render("@ Diag")+" - Run diagnostics")

		contentLines = append(contentLines, "") // Empty line

		contentLines = append(contentLines, lipgloss.NewStyle().
			Foreground(purpleColor).
			Bold(true).
			Render("Navigation:"))

		contentLines = append(contentLines, lipgloss.NewStyle().
			Foreground(lipgloss.AdaptiveColor{Light: "#6B7280", Dark: "#9CA3AF"}).
			Render("←/→ or 1-3 to navigate"))

		bodyContent = lipgloss.NewStyle().
			Width(max(10, width-8)).
			Align(lipgloss.Center).
			Render(strings.Join(contentLines, "\n"))
	} else if availableHeight >= 8 {
		// Medium content
		bodyContent = lipgloss.NewStyle().
			Width(max(10, width-8)).
			Align(lipgloss.Center).
			Render(
				lipgloss.NewStyle().
					Foreground(primaryColor).
					Bold(true).
					Render("# ICI") + " - Test links\n" +
					lipgloss.NewStyle().
						Foreground(accentColor).
						Bold(true).
						Render("@ Diag") + " - Run diagnostics")
	} else {
		// Minimal content for very small screens
		bodyContent = lipgloss.NewStyle().
			Width(max(10, width-8)).
			Align(lipgloss.Center).
			Render("Use ←/→ or 1-3")
	}

	body := bodyStyle.Render(bodyContent)

	// Only show info box if there's space
	var elements []string
	elements = append(elements, header, body)

	if availableHeight >= 12 && width >= 30 {
		infoBoxContent := lipgloss.NewStyle().
			Bold(true).
			Render("Network Diagnostics")

		infoBox := boxStyle.Width(max(20, min(width-10, 40))).Render(infoBoxContent)
		elements = append(elements, infoBox)
	}

	// Use window style that connects to tabs (following official pattern)
	content := lipgloss.JoinVertical(lipgloss.Top, elements...)
	windowWidth := max(10, m.width-windowStyle.GetHorizontalFrameSize())

	return windowStyle.Width(windowWidth).Render(content)
}

// ICI tab view with enhanced colors
func (m model) iciTabView() string {
	// Responsive header text
	var headerText string
	if m.width >= 50 {
		headerText = "Network Link Testing"
	} else if m.width >= 30 {
		headerText = "Link Testing"
	} else if m.width >= 20 {
		headerText = "Links"
	} else {
		headerText = "#"
	}

	// Enhanced list view with colorful header
	listHeader := lipgloss.NewStyle().
		Bold(true).
		Foreground(lipgloss.Color("#FFFFFF")).
		Background(secondaryColor).
		Padding(1, 2).
		Align(lipgloss.Center).
		MaxWidth(max(5, m.width-8)).
		Render(headerText)

	listView := m.iciList.View()

	var testButton string
	if m.iciTesting {
		spinnerStyle := lipgloss.NewStyle().
			Foreground(primaryColor).
			Bold(true)
		testButton = spinnerStyle.Render(m.iciSpinner.View() + " Testing connection...")
	} else {
		testButton = activeButtonStyle.Render("Select and Enter to test")
	}

	var result string
	if m.iciTestResult != "" {
		// Use different styles based on success/failure
		if strings.Contains(m.iciTestResult, "✅") {
			result = successResultStyle.Render(m.iciTestResult)
		} else {
			result = errorResultStyle.Render(m.iciTestResult)
		}
	}

	// Enhanced history with colorful header
	historyHeader := lipgloss.NewStyle().
		Bold(true).
		Foreground(lipgloss.Color("#FFFFFF")).
		Background(purpleColor).
		Padding(0, 2).
		Render("Test History")

	var historyContent string
	if len(m.iciTestHistory) > 0 {
		historyContent = strings.Join(m.iciTestHistory, "\n")
	} else {
		historyContent = lipgloss.NewStyle().
			Italic(true).
			Foreground(lipgloss.AdaptiveColor{Light: "#6B7280", Dark: "#9CA3AF"}).
			Render("No tests run yet - select a link above to get started!")
	}

	history := lipgloss.JoinVertical(lipgloss.Left, historyHeader, historyContent)

	// Use window style that connects to tabs
	content := lipgloss.JoinVertical(lipgloss.Left, listHeader, listView, testButton, result, history)
	windowWidth := max(10, m.width-windowStyle.GetHorizontalFrameSize())

	return windowStyle.Width(windowWidth).Render(content)
}

// Diag tab view with enhanced colors
func (m model) diagTabView() string {
	// Responsive header text
	var headerText string
	if m.width >= 50 {
		headerText = "CLI Network Diagnostics"
	} else if m.width >= 30 {
		headerText = "CLI Diagnostics"
	} else if m.width >= 20 {
		headerText = "Diag"
	} else {
		headerText = "@"
	}

	// Enhanced input section
	inputHeader := lipgloss.NewStyle().
		Bold(true).
		Foreground(lipgloss.Color("#FFFFFF")).
		Background(accentColor).
		Padding(1, 2).
		Align(lipgloss.Center).
		MaxWidth(max(5, m.width-8)).
		Render(headerText)

	// Responsive machine label
	var labelText string
	if m.width >= 30 {
		labelText = "Machine Name:"
	} else if m.width >= 20 {
		labelText = "Name:"
	} else {
		labelText = "PC:"
	}

	machineLabel := lipgloss.NewStyle().
		Bold(true).
		Foreground(secondaryColor).
		Render(labelText)

	input := inputStyle.Render(m.diagMachineInput.View())

	var runButton string
	if m.diagRunning {
		spinnerStyle := lipgloss.NewStyle().
			Foreground(primaryColor).
			Bold(true)
		runButton = spinnerStyle.Render(m.diagSpinner.View() + " Running diagnostics...")
	} else {
		runButton = activeButtonStyle.Render("Press Enter to run diagnostics")
	}

	// Enhanced output section
	outputHeader := lipgloss.NewStyle().
		Bold(true).
		Foreground(lipgloss.Color("#FFFFFF")).
		Background(gradientStart).
		Padding(0, 2).
		Render("CLI Output")

	outputContent := lipgloss.NewStyle().
		Border(lipgloss.RoundedBorder()).
		BorderForeground(gradientStart).
		Background(lipgloss.AdaptiveColor{Light: "#F8FAFC", Dark: "#0F172A"}).
		Padding(1, 2).
		Render(m.diagOutput.View())

	output := lipgloss.JoinVertical(lipgloss.Left, outputHeader, outputContent)

	// Enhanced table section
	tableHeader := lipgloss.NewStyle().
		Bold(true).
		Foreground(lipgloss.Color("#FFFFFF")).
		Background(successColor).
		Padding(0, 2).
		Render("Results Summary")

	tableView := m.diagTable.View()
	table := lipgloss.JoinVertical(lipgloss.Left, tableHeader, tableView)

	// Enhanced history section
	historyHeader := lipgloss.NewStyle().
		Bold(true).
		Foreground(lipgloss.Color("#FFFFFF")).
		Background(purpleColor).
		Padding(0, 2).
		Render("Command History")

	var historyContent string
	if len(m.diagHistoryList.Items()) > 0 {
		historyContent = m.diagHistoryList.View()
	} else {
		historyContent = lipgloss.NewStyle().
			Italic(true).
			Foreground(lipgloss.AdaptiveColor{Light: "#6B7280", Dark: "#9CA3AF"}).
			Render("No commands run yet - enter a machine name above!")
	}

	history := lipgloss.JoinVertical(lipgloss.Left, historyHeader, historyContent)

	// Use window style that connects to tabs
	content := lipgloss.JoinVertical(lipgloss.Left,
		inputHeader, machineLabel, input, runButton,
		output, table, history)
	windowWidth := max(10, m.width-windowStyle.GetHorizontalFrameSize())

	return windowStyle.Width(windowWidth).Render(content)
}

// Render status bar with width constraints
func (m model) renderStatusBar() string {
	statusText := fmt.Sprintf("Status: %s", m.status)

	// Adaptive keys based on available width
	var keys string
	if m.width >= 80 {
		keys = "[q] quit | [←→/hl] tabs | [1-3] direct | [?] help"
	} else if m.width >= 60 {
		keys = "[q] quit | [←→] tabs | [?] help"
	} else if m.width >= 40 {
		keys = "[q] quit | [?] help"
	} else {
		keys = "[q] quit"
	}

	fullStatus := fmt.Sprintf("%s | Keys: %s", statusText, keys)

	// Ensure status doesn't exceed width
	if lipgloss.Width(fullStatus) > m.width-4 {
		// Try with shorter status text
		maxStatusLen := max(5, m.width-len(keys)-10)
		if len(statusText) > maxStatusLen {
			statusText = statusText[:maxStatusLen-3] + "..."
		}
		fullStatus = fmt.Sprintf("%s | %s", statusText, keys)

		// If still too long, truncate the whole thing
		if lipgloss.Width(fullStatus) > m.width-4 {
			fullStatus = truncateToWidth(fullStatus, m.width-4)
		}
	}

	return statusStyle.Width(m.width).Render(fullStatus)
}

// Update ICI tab
func (m model) updateICITab(msg tea.KeyMsg) (tea.Model, tea.Cmd) {
	var cmd tea.Cmd

	if m.iciTesting {
		return m, nil // Don't handle keys while testing
	}

	switch msg.String() {
	case "enter":
		if selectedItem, ok := m.iciList.SelectedItem().(listItem); ok {
			m.modalActive = true
			m.modalContent = fmt.Sprintf("Test %s?", selectedItem.title)
		}
	}

	// Update the list
	m.iciList, cmd = m.iciList.Update(msg)
	return m, cmd
}

// Update Diag tab
func (m model) updateDiagTab(msg tea.KeyMsg) (tea.Model, tea.Cmd) {
	var cmd tea.Cmd

	if m.diagRunning {
		return m, nil // Don't handle keys while running
	}

	switch msg.String() {
	case "enter":
		if m.diagMachineInput.Value() != "" {
			m.modalActive = true
			m.modalContent = fmt.Sprintf("Run diagnostics on %s?", m.diagMachineInput.Value())
		}
	}

	// Update the input
	m.diagMachineInput, cmd = m.diagMachineInput.Update(msg)
	return m, cmd
}

// Handle modal confirmation
func (m model) handleModalConfirm() (tea.Model, tea.Cmd) {
	switch m.currentTab {
	case ICITab:
		return m.startICITest()
	case DiagTab:
		return m.startDiagTest()
	}
	return m, nil
}

// Start ICI test
func (m model) startICITest() (tea.Model, tea.Cmd) {
	if selectedItem, ok := m.iciList.SelectedItem().(listItem); ok {
		m.iciTesting = true
		m.status = "Testing " + selectedItem.title + "..."

		// Simulate test with a timer - randomly succeed or fail for realism
		return m, tea.Tick(time.Second*2, func(t time.Time) tea.Msg {
			// Simulate occasional failures for realism
			success := time.Now().UnixNano()%4 != 0 // 75% success rate

			var result string
			var status string
			if success {
				result = "✅ Connection OK - Latency: " + fmt.Sprintf("%dms", 1+time.Now().UnixNano()%10)
				status = "OK"
			} else {
				result = "❌ Connection Failed - Timeout"
				status = "FAILED"
			}

			return iciTestCompleteMsg{
				link:   selectedItem.title,
				result: result,
				status: status,
			}
		})
	}
	return m, nil
}

// Start Diag test
func (m model) startDiagTest() (tea.Model, tea.Cmd) {
	machine := strings.TrimSpace(m.diagMachineInput.Value())
	if machine == "" {
		m.status = "Error: Machine name cannot be empty"
		return m, nil
	}

	m.diagRunning = true
	m.status = "Running diagnostics on " + machine + "..."

	// Simulate CLI run with a timer - add some variability
	return m, tea.Tick(time.Second*3, func(t time.Time) tea.Msg {
		// Simulate different outcomes based on machine name
		success := !strings.Contains(strings.ToLower(machine), "offline") && time.Now().UnixNano()%5 != 0 // 80% success rate

		var output, status string
		if success {
			latency := 1 + time.Now().UnixNano()%20 // 1-20ms latency
			output = fmt.Sprintf("Pinging %s...\nReply from %s: bytes=32 time=%dms TTL=64\nReply from %s: bytes=32 time=%dms TTL=64\nPing statistics: 2 packets transmitted, 2 received, 0%% packet loss",
				machine, machine, latency, machine, latency+1)
			status = "Online"
		} else {
			output = fmt.Sprintf("Pinging %s...\nRequest timed out.\nRequest timed out.\nPing statistics: 2 packets transmitted, 0 received, 100%% packet loss", machine)
			status = "Offline"
		}

		return diagTestCompleteMsg{
			machine: machine,
			output:  output,
			status:  status,
		}
	})
}

// Modal overlay function
func (m model) overlayModal(base string) string {
	var modal string
	if m.showingHelp {
		modal = modalStyle.Render(m.modalContent + "\n\nPress any key to close")
	} else {
		modal = modalStyle.Render(m.modalContent + "\n\n[y] Yes [n] No")
	}
	return overlay(base, modal, (m.width-lipgloss.Width(modal))/2, (m.height-lipgloss.Height(modal))/2)
}

// Custom overlay function (since lipgloss doesn't have built-in overlay in older versions)
func overlay(base, overlay string, x, y int) string {
	baseLines := strings.Split(base, "\n")
	overlayLines := strings.Split(overlay, "\n")

	for i, line := range overlayLines {
		if y+i >= 0 && y+i < len(baseLines) {
			baseLine := baseLines[y+i]
			if x >= 0 && x < len(baseLine) {
				before := baseLine[:x]
				after := ""
				if x+len(line) < len(baseLine) {
					after = baseLine[x+len(line):]
				}
				baseLines[y+i] = before + line + after
			}
		}
	}

	return strings.Join(baseLines, "\n")
}

// Custom message types for test completion
type iciTestCompleteMsg struct {
	link   string
	result string
	status string
}

type diagTestCompleteMsg struct {
	machine string
	output  string
	status  string
}

// Get help text for the help modal
func getHelpText() string {
	return `ICI Network Diagnostics - Help

NAVIGATION:
  ←/→ or h/l    Switch between tabs
  1, 2, 3       Jump directly to any tab
  q, Esc        Quit application
  ?, h          Show this help screen

HOME TAB:
  Welcome screen with app information
  Navigation guide and feature overview

LINKS TAB:
  ↑/↓ or j/k    Navigate through network links
  Enter         Test selected link
  y/n           Confirm or cancel test operations

DIAGNOSTICS TAB:
  Type          Enter machine name in input
  Enter         Run CLI diagnostics with live output
  y/n           Confirm or cancel diagnostic runs
  ↑/↓ or j/k    Browse command history

FEATURES:
  • Fully responsive design adapts to terminal size
  • Adaptive colors for light and dark themes
  • Real network simulation with random failures
  • Animations and visual feedback
  • Professional styling throughout`
}

// Tab border helper function (following official Lipgloss pattern)
func tabBorderWithBottom(left, middle, right string) lipgloss.Border {
	border := lipgloss.RoundedBorder()
	border.BottomLeft = left
	border.Bottom = middle
	border.BottomRight = right
	return border
}

// Utility functions for max and min
func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// Helper function to strip ANSI escape codes for accurate width calculation
func stripAnsiCodes(s string) string {
	// Simple regex to match ANSI escape sequences
	// This is a basic implementation - for production use a proper ANSI parser
	result := ""
	inEscape := false
	for i, r := range s {
		if r == '\x1b' && i+1 < len(s) && s[i+1] == '[' {
			inEscape = true
			continue
		}
		if inEscape {
			if r == 'm' {
				inEscape = false
			}
			continue
		}
		result += string(r)
	}
	return result
}

// Helper function to truncate a line to fit within width
func truncateToWidth(line string, maxWidth int) string {
	if maxWidth <= 0 {
		return ""
	}
	if maxWidth <= 3 {
		return strings.Repeat(".", min(maxWidth, 3))
	}

	clean := stripAnsiCodes(line)
	if lipgloss.Width(clean) <= maxWidth {
		return line
	}

	// Simple truncation - preserve as much as possible
	runes := []rune(clean)
	if len(runes) > maxWidth-3 {
		truncated := string(runes[:max(0, maxWidth-3)])
		if maxWidth >= 3 {
			return truncated + "..."
		}
		return truncated
	}
	return string(runes)
}

func main() {
	// Initialize the model
	m := initialModel()

	// Start the program
	p := tea.NewProgram(m, tea.WithAltScreen(), tea.WithMouseCellMotion())
	if _, err := p.Run(); err != nil {
		fmt.Printf("Error running program: %v", err)
		os.Exit(1)
	}
}
