package main

import (
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/charmbracelet/bubbles/list"
	"github.com/charmbracelet/bubbles/progress"
	"github.com/charmbracelet/bubbles/spinner"
	"github.com/charmbracelet/bubbles/table"
	"github.com/charmbracelet/bubbles/textinput"
	"github.com/charmbracelet/bubbles/viewport"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
)

// Tab constants
const (
	MainTab = iota
	ICITab
	DiagTab
)

// Tab names and icons
var (
	tabNames = []string{"Main", "ICI", "Diag"}
	tabIcons = []string{"🏠", "🔗", "⚙️"}
)

// Model represents the main application state
type model struct {
	// Global state
	width      int
	height     int
	loading    bool
	currentTab int

	// Loading screen
	prog progress.Model

	// Modal system
	modalActive  bool
	modalContent string

	// Status
	status string

	// ICI Tab
	iciList        list.Model
	iciTesting     bool
	iciSpinner     spinner.Model
	iciTestResult  string
	iciTestHistory []string

	// Diag Tab
	diagMachineInput textinput.Model
	diagRunning      bool
	diagSpinner      spinner.Model
	diagOutput       viewport.Model
	diagTable        table.Model
	diagHistoryList  list.Model
}

// Initialize the application
func (m model) Init() tea.Cmd {
	return tea.Batch(
		m.prog.Init(),
		m.iciSpinner.Tick,
		m.diagSpinner.Tick,
		tea.Tick(time.Millisecond*50, func(t time.Time) tea.Msg {
			return tickMsg(t)
		}),
	)
}

// Custom message types
type tickMsg time.Time

// List item implementation for bubbles/list
type listItem struct {
	title, desc string
}

func (i listItem) Title() string       { return i.title }
func (i listItem) Description() string { return i.desc }
func (i listItem) FilterValue() string { return i.title }

// Update handles all events and state changes
func (m model) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	var cmd tea.Cmd
	var cmds []tea.Cmd

	switch msg := msg.(type) {
	case tea.WindowSizeMsg:
		m.width = msg.Width
		m.height = msg.Height

		// Update component sizes
		m.prog.Width = msg.Width - 4
		m.iciList.SetSize(msg.Width-4, msg.Height-15)
		m.diagOutput.Width = msg.Width - 4
		m.diagOutput.Height = 10
		m.diagHistoryList.SetSize(msg.Width-4, 8)

		return m, nil

	case tea.KeyMsg:
		// Global key handling
		switch msg.String() {
		case "ctrl+c", "q", "esc":
			return m, tea.Quit
		}

		// Modal handling
		if m.modalActive {
			switch msg.String() {
			case "y", "Y":
				m.modalActive = false
				return m.handleModalConfirm()
			case "n", "N":
				m.modalActive = false
				m.modalContent = ""
				return m, nil
			}
			return m, nil
		}

		// Loading screen handling
		if m.loading {
			if msg.String() == "q" {
				m.loading = false
				m.status = "Ready"
			}
			return m, nil
		}

		// Tab navigation
		switch msg.String() {
		case "left", "h":
			m.currentTab = (m.currentTab - 1 + len(tabNames)) % len(tabNames)
		case "right", "l":
			m.currentTab = (m.currentTab + 1) % len(tabNames)
		case "1":
			m.currentTab = MainTab
		case "2":
			m.currentTab = ICITab
		case "3":
			m.currentTab = DiagTab
		}

		// Tab-specific key handling
		switch m.currentTab {
		case ICITab:
			return m.updateICITab(msg)
		case DiagTab:
			return m.updateDiagTab(msg)
		}

	case tea.MouseMsg:
		// Handle mouse clicks on tabs
		if msg.Type == tea.MouseLeft && msg.Y == 0 {
			// Calculate which tab was clicked based on X position
			tabWidth := 8 // Approximate width of each tab
			clickedTab := msg.X / tabWidth
			if clickedTab >= 0 && clickedTab < len(tabNames) {
				m.currentTab = clickedTab
			}
		}

	case tickMsg:
		// Handle loading progress
		if m.loading {
			if m.prog.Percent() >= 1.0 {
				m.loading = false
				m.status = "Ready"
			} else {
				cmd = m.prog.IncrPercent(0.02) // Increment by 2% each tick
				cmds = append(cmds, cmd)
			}
		}

		// Continue ticking
		cmds = append(cmds, tea.Tick(time.Millisecond*50, func(t time.Time) tea.Msg {
			return tickMsg(t)
		}))

	case iciTestCompleteMsg:
		m.iciTesting = false
		m.iciTestResult = msg.result
		m.status = "ICI test completed"

		// Add to history
		timestamp := time.Now().Format("2006-01-02 15:04:05")
		historyEntry := fmt.Sprintf("%s | %s | %s", msg.link, "OK", timestamp)
		m.iciTestHistory = append(m.iciTestHistory, historyEntry)

	case diagTestCompleteMsg:
		m.diagRunning = false
		m.status = "Diagnostics completed"

		// Update output
		m.diagOutput.SetContent(msg.output)

		// Add to table
		newRow := table.Row{msg.machine, msg.status, "Ping successful"}
		m.diagTable.SetRows(append(m.diagTable.Rows(), newRow))

		// Add to history
		timestamp := time.Now().Format("2006-01-02 15:04:05")
		historyItem := listItem{
			title: fmt.Sprintf("%s - %s", msg.machine, timestamp),
			desc:  fmt.Sprintf("Status: %s", msg.status),
		}

		// Insert at beginning of history
		items := m.diagHistoryList.Items()
		newItems := append([]list.Item{historyItem}, items...)
		m.diagHistoryList.SetItems(newItems)

		// Clear input
		m.diagMachineInput.SetValue("")
	}

	// Update spinners
	m.iciSpinner, cmd = m.iciSpinner.Update(msg)
	cmds = append(cmds, cmd)

	m.diagSpinner, cmd = m.diagSpinner.Update(msg)
	cmds = append(cmds, cmd)

	return m, tea.Batch(cmds...)
}

// View renders the application
func (m model) View() string {
	if m.loading {
		return m.loadingView()
	}

	// Render tabs
	tabs := m.renderTabs()

	// Render current tab content
	var content string
	switch m.currentTab {
	case MainTab:
		content = m.mainTabView()
	case ICITab:
		content = m.iciTabView()
	case DiagTab:
		content = m.diagTabView()
	}

	// Render status bar
	statusBar := m.renderStatusBar()

	// Combine all elements
	full := lipgloss.JoinVertical(lipgloss.Left, tabs, content, statusBar)
	full = lipgloss.Place(m.width, m.height, lipgloss.Left, lipgloss.Top, full)

	// Add modal overlay if active
	if m.modalActive {
		full = m.overlayModal(full)
	}

	return full
}

// Styles
var (
	// Tab styles
	activeTab = lipgloss.NewStyle().
			Bold(true).
			Foreground(lipgloss.Color("205")).
			Background(lipgloss.Color("235")).
			Padding(0, 1)

	tab = lipgloss.NewStyle().
		Foreground(lipgloss.Color("241")).
		Padding(0, 1)

	tabGap = lipgloss.NewStyle().
		Background(lipgloss.Color("235"))

	// Content styles
	contentStyle = lipgloss.NewStyle().
			Padding(1, 2)

	headerStyle = lipgloss.NewStyle().
			Bold(true).
			Foreground(lipgloss.Color("205")).
			Align(lipgloss.Center).
			Border(lipgloss.DoubleBorder()).
			Padding(1, 2)

	bodyStyle = lipgloss.NewStyle().
			Italic(true).
			Foreground(lipgloss.Color("246")).
			Align(lipgloss.Center).
			MaxHeight(5)

	boxStyle = lipgloss.NewStyle().
			Border(lipgloss.RoundedBorder()).
			BorderForeground(lipgloss.Color("62")).
			Padding(1, 2).
			Align(lipgloss.Center)

	// Button styles
	activeButtonStyle = lipgloss.NewStyle().
				Bold(true).
				Foreground(lipgloss.Color("15")).
				Background(lipgloss.Color("205")).
				Padding(0, 2).
				Margin(1, 0)

	// Input styles
	inputStyle = lipgloss.NewStyle().
			Border(lipgloss.NormalBorder()).
			BorderForeground(lipgloss.Color("62")).
			Padding(0, 1).
			Margin(1, 0)

	// Loading styles
	loadingStyle = lipgloss.NewStyle().
			Align(lipgloss.Center).
			Foreground(lipgloss.Color("205")).
			Bold(true)

	// Modal styles
	modalStyle = lipgloss.NewStyle().
			Border(lipgloss.RoundedBorder()).
			BorderForeground(lipgloss.Color("205")).
			Background(lipgloss.Color("235")).
			Padding(1, 2).
			Align(lipgloss.Center)

	// Status bar style
	statusStyle = lipgloss.NewStyle().
			Foreground(lipgloss.Color("241")).
			Background(lipgloss.Color("235")).
			Padding(0, 1)
)

// Initialize the model with default values
func initialModel() model {
	// Create progress bar
	prog := progress.New(progress.WithDefaultGradient())
	prog.Width = 40

	// Create spinners
	iciSpinner := spinner.New()
	iciSpinner.Spinner = spinner.Globe
	iciSpinner.Style = lipgloss.NewStyle().Foreground(lipgloss.Color("205"))

	diagSpinner := spinner.New()
	diagSpinner.Spinner = spinner.Dot
	diagSpinner.Style = lipgloss.NewStyle().Foreground(lipgloss.Color("205"))

	// Create ICI list
	iciItems := []list.Item{
		listItem{title: "Fiber Link 1", desc: "Primary fiber connection"},
		listItem{title: "Fiber Link 2", desc: "Secondary fiber connection"},
		listItem{title: "Copper Link 1", desc: "Primary copper connection"},
		listItem{title: "Copper Link 2", desc: "Secondary copper connection"},
	}
	iciList := list.New(iciItems, list.NewDefaultDelegate(), 0, 0)
	iciList.Title = "Select Link to Test"
	iciList.SetShowStatusBar(false)
	iciList.SetFilteringEnabled(false)

	// Create text input for diag
	diagInput := textinput.New()
	diagInput.Placeholder = "Enter machine name..."
	diagInput.Focus()
	diagInput.CharLimit = 50
	diagInput.Width = 30

	// Create viewport for diag output
	diagOutput := viewport.New(0, 10)
	diagOutput.SetContent("No output yet...")

	// Create table for diag results
	diagTable := table.New(
		table.WithColumns([]table.Column{
			{Title: "Host", Width: 20},
			{Title: "Status", Width: 10},
			{Title: "Details", Width: 30},
		}),
		table.WithRows([]table.Row{}),
		table.WithFocused(true),
		table.WithHeight(5),
	)

	// Create history list for diag
	diagHistoryList := list.New([]list.Item{}, list.NewDefaultDelegate(), 0, 0)
	diagHistoryList.Title = "Command History"
	diagHistoryList.SetShowStatusBar(false)
	diagHistoryList.SetFilteringEnabled(false)

	return model{
		loading:          true,
		currentTab:       MainTab,
		prog:             prog,
		status:           "Loading...",
		iciList:          iciList,
		iciSpinner:       iciSpinner,
		iciTestHistory:   []string{},
		diagMachineInput: diagInput,
		diagSpinner:      diagSpinner,
		diagOutput:       diagOutput,
		diagTable:        diagTable,
		diagHistoryList:  diagHistoryList,
	}
}

// Loading screen view
func (m model) loadingView() string {
	pad := lipgloss.NewStyle().Padding(1, 0).Align(lipgloss.Center).Render(m.prog.View())
	return loadingStyle.Render("Loading...\n\n" + pad + "\n\nPress q to skip")
}

// Render tabs at the top
func (m model) renderTabs() string {
	var renderedTabs []string
	for i, t := range tabNames {
		tabText := tabIcons[i] + " " + t
		style := tab
		if i == m.currentTab {
			style = activeTab
		}
		renderedTabs = append(renderedTabs, style.Render(tabText))
	}

	tabRow := lipgloss.JoinHorizontal(lipgloss.Top, renderedTabs...)
	gap := tabGap.Render(strings.Repeat(" ", max(0, m.width-lipgloss.Width(tabRow)-2)))
	return lipgloss.JoinHorizontal(lipgloss.Bottom, tabRow, gap)
}

// Main tab view
func (m model) mainTabView() string {
	width := m.width
	if width < 10 {
		width = 10
	}

	header := headerStyle.Width(width - 4).Render("Welcome to ICI Network Diagnostics")
	body := bodyStyle.Width(width - 4).Render("Use tabs to navigate between different diagnostic tools:\n\n🔗 ICI - Test network links\n⚙️ Diag - Run CLI diagnostics\n\nUse arrow keys or h/l to switch tabs\nPress 1, 2, 3 for direct navigation")
	infoBox := boxStyle.Width(max(20, width-20)).Render("App for testing network connectivity and diagnostics")

	return contentStyle.Render(lipgloss.JoinVertical(lipgloss.Top, header, body, infoBox))
}

// ICI tab view
func (m model) iciTabView() string {
	listView := m.iciList.View()

	var testButton string
	if m.iciTesting {
		testButton = m.iciSpinner.View() + " Testing..."
	} else {
		testButton = activeButtonStyle.Render("Select and Enter to test")
	}

	var result string
	if m.iciTestResult != "" {
		result = boxStyle.Render(m.iciTestResult)
	}

	var history string
	if len(m.iciTestHistory) > 0 {
		history = "Test History:\n" + strings.Join(m.iciTestHistory, "\n")
	} else {
		history = "Test History:\nNo tests run yet"
	}

	return contentStyle.Render(lipgloss.JoinVertical(lipgloss.Left, listView, testButton, result, history))
}

// Diag tab view
func (m model) diagTabView() string {
	input := inputStyle.Render(m.diagMachineInput.View())

	var runButton string
	if m.diagRunning {
		runButton = m.diagSpinner.View() + " Running..."
	} else {
		runButton = activeButtonStyle.Render("Press Enter to run CLI")
	}

	output := m.diagOutput.View()
	tableView := m.diagTable.View()

	var historyList string
	if len(m.diagHistoryList.Items()) > 0 {
		historyList = m.diagHistoryList.View()
	} else {
		historyList = "No history yet"
	}

	return contentStyle.Render(lipgloss.JoinVertical(lipgloss.Left,
		"Machine Name:", input, runButton,
		"Output:", output,
		"Results Table:", tableView,
		"History:", historyList))
}

// Render status bar
func (m model) renderStatusBar() string {
	keys := "[q] quit | [←→/hl] tabs | [1-3] direct"
	status := fmt.Sprintf("Status: %s | Keys: %s", m.status, keys)
	return statusStyle.Width(m.width).Render(status)
}

// Update ICI tab
func (m model) updateICITab(msg tea.KeyMsg) (tea.Model, tea.Cmd) {
	var cmd tea.Cmd

	if m.iciTesting {
		return m, nil // Don't handle keys while testing
	}

	switch msg.String() {
	case "enter":
		if selectedItem, ok := m.iciList.SelectedItem().(listItem); ok {
			m.modalActive = true
			m.modalContent = fmt.Sprintf("Test %s?", selectedItem.title)
		}
	}

	// Update the list
	m.iciList, cmd = m.iciList.Update(msg)
	return m, cmd
}

// Update Diag tab
func (m model) updateDiagTab(msg tea.KeyMsg) (tea.Model, tea.Cmd) {
	var cmd tea.Cmd

	if m.diagRunning {
		return m, nil // Don't handle keys while running
	}

	switch msg.String() {
	case "enter":
		if m.diagMachineInput.Value() != "" {
			m.modalActive = true
			m.modalContent = fmt.Sprintf("Run diagnostics on %s?", m.diagMachineInput.Value())
		}
	}

	// Update the input
	m.diagMachineInput, cmd = m.diagMachineInput.Update(msg)
	return m, cmd
}

// Handle modal confirmation
func (m model) handleModalConfirm() (tea.Model, tea.Cmd) {
	switch m.currentTab {
	case ICITab:
		return m.startICITest()
	case DiagTab:
		return m.startDiagTest()
	}
	return m, nil
}

// Start ICI test
func (m model) startICITest() (tea.Model, tea.Cmd) {
	if selectedItem, ok := m.iciList.SelectedItem().(listItem); ok {
		m.iciTesting = true
		m.status = "Testing " + selectedItem.title + "..."

		// Simulate test with a timer
		return m, tea.Tick(time.Second*2, func(t time.Time) tea.Msg {
			return iciTestCompleteMsg{
				link:   selectedItem.title,
				result: "✅ Connection OK - Latency: 2ms",
			}
		})
	}
	return m, nil
}

// Start Diag test
func (m model) startDiagTest() (tea.Model, tea.Cmd) {
	machine := m.diagMachineInput.Value()
	m.diagRunning = true
	m.status = "Running diagnostics on " + machine + "..."

	// Simulate CLI run with a timer
	return m, tea.Tick(time.Second*3, func(t time.Time) tea.Msg {
		return diagTestCompleteMsg{
			machine: machine,
			output:  fmt.Sprintf("Pinging %s...\nReply from %s: bytes=32 time=1ms TTL=64\nReply from %s: bytes=32 time=2ms TTL=64\nPing statistics: 2 packets transmitted, 2 received, 0%% packet loss", machine, machine, machine),
			status:  "Online",
		}
	})
}

// Modal overlay function
func (m model) overlayModal(base string) string {
	modal := modalStyle.Render(m.modalContent + "\n\n[y] Yes [n] No")
	return overlay(base, modal, (m.width-lipgloss.Width(modal))/2, (m.height-lipgloss.Height(modal))/2)
}

// Custom overlay function (since lipgloss doesn't have built-in overlay in older versions)
func overlay(base, overlay string, x, y int) string {
	baseLines := strings.Split(base, "\n")
	overlayLines := strings.Split(overlay, "\n")

	for i, line := range overlayLines {
		if y+i >= 0 && y+i < len(baseLines) {
			baseLine := baseLines[y+i]
			if x >= 0 && x < len(baseLine) {
				before := baseLine[:x]
				after := ""
				if x+len(line) < len(baseLine) {
					after = baseLine[x+len(line):]
				}
				baseLines[y+i] = before + line + after
			}
		}
	}

	return strings.Join(baseLines, "\n")
}

// Custom message types for test completion
type iciTestCompleteMsg struct {
	link   string
	result string
}

type diagTestCompleteMsg struct {
	machine string
	output  string
	status  string
}

// Utility function for max
func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

func main() {
	// Initialize the model
	m := initialModel()

	// Start the program
	p := tea.NewProgram(m, tea.WithAltScreen(), tea.WithMouseCellMotion())
	if _, err := p.Run(); err != nil {
		fmt.Printf("Error running program: %v", err)
		os.Exit(1)
	}
}
