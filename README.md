# 🌟 ICI Network Diagnostics TUI

A **flashy and colorful** Terminal User Interface (TUI) application for network diagnostics built with Go, Bubble Tea, Lipgloss, and Bubbles. Features vibrant colors, beautiful animations, and professional styling inspired by modern design principles.

## Features

### 🏠 Main Tab
- Welcome screen with application information
- Navigation instructions
- Clean, centered layout with styled borders

### 🔗 ICI Tab (Link Testing)
- Interactive list of network links (Fiber/Copper)
- Test individual links with confirmation modal
- Real-time testing with animated spinner
- Results display with success/failure indicators
- Test history with timestamps
- Realistic simulation with 75% success rate

### ⚙️ Diag Tab (CLI Diagnostics)
- Text input for machine names
- Confirmation modal before running diagnostics
- Simulated ping output in scrollable viewport
- Results table with host, status, and details
- Command history list
- Input validation and error handling
- Realistic simulation with 80% success rate (lower for "offline" machines)

### 🎨 UI Features - **Flashy & Colorful Design**
- **Vibrant Color Palette**: Hot pink, teal, yellow, mint green, coral, and purple accents
- **Adaptive Colors**: Automatically adjusts for light and dark terminal themes
- **Gradient Effects**: Beautiful color transitions and professional styling
- **Enhanced Typography**: Bold headers, italic text, and perfect contrast
- **Animated Elements**: Colorful spinners, progress bars, and loading messages
- **Responsive Design**: Adapts to terminal size changes with minimum constraints
- **Loading Screen**: Flashy 3-second progress bar with animated messages and skip option
- **Tab Navigation**: Colorful tabs with rounded borders, keyboard (←/→, h/l, 1-3) and mouse support
- **Modal System**: Dramatic confirmation dialogs and comprehensive help screen
- **Status Bar**: Gradient-styled status bar with dynamic updates and keyboard shortcuts
- **Help System**: Press `?` for colorful, comprehensive help with emojis
- **Professional Borders**: Thick, rounded, and double borders throughout
- **Visual Hierarchy**: Color-coded sections with distinct styling for each component

## Installation

```bash
# Clone or create the project
go mod init tui-network-diagnostics

# Install dependencies
go get github.com/charmbracelet/bubbletea@latest
go get github.com/charmbracelet/bubbles@latest

# Build the application
go build -o tui-app.exe .

# Or run directly
go run .
```

## Usage

### Navigation
- **←/→** or **h/l**: Switch between tabs
- **1, 2, 3**: Jump directly to specific tab
- **q**, **Esc**: Quit application
- **?**: Show help screen

### ICI Tab
- **↑/↓** or **j/k**: Navigate link list
- **Enter**: Test selected link
- **y/n**: Confirm or cancel test

### Diag Tab
- **Type**: Enter machine name in input field
- **Enter**: Run diagnostics on entered machine
- **y/n**: Confirm or cancel diagnostic run
- **↑/↓** or **j/k**: Navigate command history

## Technical Details

### Architecture
- **Bubble Tea**: Event-driven TUI framework with Model-Update-View pattern
- **Lipgloss**: Styling and layout system for terminal interfaces
- **Bubbles**: Pre-built UI components (lists, tables, inputs, etc.)

### Components Used
- `progress.Model`: Loading screen progress bar
- `spinner.Model`: Animated loading indicators during tests
- `list.Model`: Scrollable lists for links and history
- `table.Model`: Tabular display of diagnostic results
- `textinput.Model`: Text input for machine names
- `viewport.Model`: Scrollable text area for CLI output

### Key Features
- **Flashy Visual Design**: Inspired by modern UI/UX with vibrant color schemes
- **Adaptive Color System**: Automatically switches between light/dark themes
- **Professional Animations**: Smooth spinners, progress bars, and transitions
- **Responsive Sizing**: All components adapt to terminal dimensions with minimum constraints
- **Enhanced Error Handling**: Input validation with colorful error messages
- **Realistic Simulation**: Tests include random failures, variable latency, and smart logic
- **Dramatic Modal System**: Custom overlay implementation with thick borders and gradients
- **Full Mouse Support**: Click on colorful tabs for navigation
- **Comprehensive Help System**: Colorful in-app documentation with emojis and clear sections
- **Visual Feedback**: Color-coded results (green for success, red for failure)
- **Professional Typography**: Bold headers, italic descriptions, and perfect contrast ratios

### File Structure
```
.
├── main.go          # Main application code
├── go.mod           # Go module definition
├── go.sum           # Dependency checksums
├── README.md        # This file
└── tui-app.exe      # Compiled binary (after build)
```

## Development

The application follows the Bubble Tea pattern:

1. **Model**: Holds all application state
2. **Init**: Sets up initial state and components
3. **Update**: Handles all events (keys, mouse, timers)
4. **View**: Renders the current state to terminal

Key design decisions:
- Minimum size constraints prevent crashes
- Component updates are batched for performance
- Modal system uses custom overlay function
- Responsive design adapts to various terminal sizes
- Realistic test simulations provide engaging user experience

## Dependencies

- `github.com/charmbracelet/bubbletea`: TUI framework
- `github.com/charmbracelet/lipgloss`: Styling and layout
- `github.com/charmbracelet/bubbles`: UI components

## License

This project is a demonstration of TUI development with Go and Bubble Tea.
