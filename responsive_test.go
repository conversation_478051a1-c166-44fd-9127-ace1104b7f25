package main

import (
	"fmt"
	"strings"
	"testing"

	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
)

// Test helper to create a model with specific dimensions
func createTestModel(width, height int) model {
	m := initialModel()
	m.width = width
	m.height = height
	m.loading = false // Skip loading for tests

	// Update component sizes
	m.updateComponentSizes()

	return m
}

// Helper method to update component sizes (extracted from WindowSizeMsg handling)
func (m *model) updateComponentSizes() {
	minWidth := max(20, m.width-4)
	minHeight := max(5, m.height-15)

	m.prog.Width = minWidth

	// ICI list sizing
	if minWidth > 0 && minHeight > 0 {
		m.iciList.SetSize(minWidth, minHeight)
	}

	// Diag components sizing
	m.diagOutput.Width = minWidth
	m.diagOutput.Height = max(5, min(15, m.height/3))

	// Diag history list sizing
	historyHeight := max(3, min(10, m.height/4))
	if minWidth > 0 && historyHeight > 0 {
		m.diagHistoryList.SetSize(minWidth, historyHeight)
	}
}

// Test that content doesn't overflow screen boundaries
func TestContentBoundaries(t *testing.T) {
	testCases := []struct {
		name   string
		width  int
		height int
		tab    int
	}{
		{"Small Terminal", 40, 20, MainTab},
		{"Medium Terminal", 80, 24, MainTab},
		{"Large Terminal", 120, 40, MainTab},
		{"Very Small Terminal", 20, 10, MainTab},
		{"Wide Terminal", 200, 30, MainTab},
		{"Tall Terminal", 80, 60, MainTab},
		{"ICI Tab Small", 40, 20, ICITab},
		{"ICI Tab Medium", 80, 24, ICITab},
		{"ICI Tab Large", 120, 40, ICITab},
		{"Diag Tab Small", 40, 20, DiagTab},
		{"Diag Tab Medium", 80, 24, DiagTab},
		{"Diag Tab Large", 120, 40, DiagTab},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			m := createTestModel(tc.width, tc.height)
			m.currentTab = tc.tab

			// Get the rendered view
			view := m.View()

			// Check content boundaries
			if err := checkContentBoundaries(view, tc.width, tc.height); err != nil {
				t.Errorf("Content overflow in %s: %v", tc.name, err)

				// Print the view for debugging
				t.Logf("View content:\n%s", view)
			}
		})
	}
}

// Check if content overflows screen boundaries
func checkContentBoundaries(view string, maxWidth, maxHeight int) error {
	lines := strings.Split(view, "\n")

	// Check height overflow
	if len(lines) > maxHeight {
		return fmt.Errorf("height overflow: %d lines > %d max height", len(lines), maxHeight)
	}

	// Check width overflow for each line
	for i, line := range lines {
		// Remove ANSI escape sequences for accurate width calculation
		cleanLine := stripAnsiCodes(line)
		lineWidth := lipgloss.Width(cleanLine)

		if lineWidth > maxWidth {
			return fmt.Errorf("width overflow on line %d: %d chars > %d max width. Line: %q",
				i+1, lineWidth, maxWidth, cleanLine)
		}
	}

	return nil
}

// Test responsive behavior when window size changes
func TestResponsiveResize(t *testing.T) {
	m := createTestModel(80, 24)

	// Test various resize scenarios
	resizeTests := []struct {
		name      string
		newWidth  int
		newHeight int
	}{
		{"Shrink Both", 40, 15},
		{"Expand Both", 120, 40},
		{"Shrink Width", 50, 24},
		{"Expand Width", 150, 24},
		{"Shrink Height", 80, 15},
		{"Expand Height", 80, 50},
		{"Minimum Size", 20, 10},
		{"Very Large", 200, 80},
	}

	for _, rt := range resizeTests {
		t.Run(rt.name, func(t *testing.T) {
			// Simulate window resize
			resizeMsg := tea.WindowSizeMsg{
				Width:  rt.newWidth,
				Height: rt.newHeight,
			}

			updatedModel, _ := m.Update(resizeMsg)
			m = updatedModel.(model)

			// Test all tabs after resize
			for tab := 0; tab < len(tabNames); tab++ {
				m.currentTab = tab
				view := m.View()

				if err := checkContentBoundaries(view, rt.newWidth, rt.newHeight); err != nil {
					t.Errorf("Resize %s failed for tab %d: %v", rt.name, tab, err)
				}
			}
		})
	}
}

// Test that components have minimum viable sizes
func TestMinimumSizes(t *testing.T) {
	// Test extremely small terminals
	verySmallSizes := []struct {
		width  int
		height int
	}{
		{10, 5},
		{15, 8},
		{20, 10},
		{25, 12},
	}

	for _, size := range verySmallSizes {
		t.Run(fmt.Sprintf("Size_%dx%d", size.width, size.height), func(t *testing.T) {
			m := createTestModel(size.width, size.height)

			// Test all tabs
			for tab := 0; tab < len(tabNames); tab++ {
				m.currentTab = tab
				view := m.View()

				// Should not panic or overflow
				if err := checkContentBoundaries(view, size.width, size.height); err != nil {
					t.Errorf("Minimum size test failed for %dx%d tab %d: %v",
						size.width, size.height, tab, err)
				}

				// Should have some content (not empty)
				if strings.TrimSpace(view) == "" {
					t.Errorf("View is empty for size %dx%d tab %d", size.width, size.height, tab)
				}
			}
		})
	}
}

// Test loading screen responsiveness
func TestLoadingScreenBoundaries(t *testing.T) {
	testSizes := []struct {
		width  int
		height int
	}{
		{40, 20},
		{80, 24},
		{120, 40},
		{20, 10},
	}

	for _, size := range testSizes {
		t.Run(fmt.Sprintf("Loading_%dx%d", size.width, size.height), func(t *testing.T) {
			m := createTestModel(size.width, size.height)
			m.loading = true // Enable loading screen

			view := m.loadingView()

			if err := checkContentBoundaries(view, size.width, size.height); err != nil {
				t.Errorf("Loading screen overflow for %dx%d: %v", size.width, size.height, err)
			}
		})
	}
}

// stripAnsiCodes is now defined in main.go

// Benchmark test for performance with different screen sizes
func BenchmarkViewRendering(b *testing.B) {
	sizes := []struct {
		name   string
		width  int
		height int
	}{
		{"Small", 40, 20},
		{"Medium", 80, 24},
		{"Large", 120, 40},
		{"XLarge", 200, 60},
	}

	for _, size := range sizes {
		b.Run(size.name, func(b *testing.B) {
			m := createTestModel(size.width, size.height)

			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				// Test all tabs
				for tab := 0; tab < len(tabNames); tab++ {
					m.currentTab = tab
					_ = m.View()
				}
			}
		})
	}
}

// Test that tab rendering doesn't overflow
func TestTabRendering(t *testing.T) {
	testSizes := []struct {
		width  int
		height int
	}{
		{30, 10},  // Very narrow
		{50, 15},  // Small
		{80, 24},  // Medium
		{120, 40}, // Large
	}

	for _, size := range testSizes {
		t.Run(fmt.Sprintf("Tabs_%dx%d", size.width, size.height), func(t *testing.T) {
			m := createTestModel(size.width, size.height)

			// Test tab rendering specifically
			tabs := m.renderTabs()

			// Check that tabs don't overflow width
			tabLines := strings.Split(tabs, "\n")
			for i, line := range tabLines {
				cleanLine := stripAnsiCodes(line)
				lineWidth := lipgloss.Width(cleanLine)

				if lineWidth > size.width {
					t.Errorf("Tab line %d overflows: %d chars > %d width. Line: %q",
						i+1, lineWidth, size.width, cleanLine)
				}
			}
		})
	}
}

// Test status bar rendering
func TestStatusBarRendering(t *testing.T) {
	testSizes := []int{30, 50, 80, 120, 200}

	for _, width := range testSizes {
		t.Run(fmt.Sprintf("StatusBar_%d", width), func(t *testing.T) {
			m := createTestModel(width, 24)

			statusBar := m.renderStatusBar()
			cleanStatus := stripAnsiCodes(statusBar)
			statusWidth := lipgloss.Width(cleanStatus)

			if statusWidth > width {
				t.Errorf("Status bar overflows: %d chars > %d width. Content: %q",
					statusWidth, width, cleanStatus)
			}
		})
	}
}

// Test modal boundaries
func TestModalBoundaries(t *testing.T) {
	testSizes := []struct {
		width  int
		height int
	}{
		{40, 20},
		{80, 24},
		{120, 40},
	}

	for _, size := range testSizes {
		t.Run(fmt.Sprintf("Modal_%dx%d", size.width, size.height), func(t *testing.T) {
			m := createTestModel(size.width, size.height)
			m.modalActive = true
			m.modalContent = "Test modal content"

			view := m.View()

			if err := checkContentBoundaries(view, size.width, size.height); err != nil {
				t.Errorf("Modal overflow for %dx%d: %v", size.width, size.height, err)
			}
		})
	}
}
